<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F3E5F5"
    android:padding="16dp">

    <TextView
        android:id="@+id/tv_list_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="List Fragment"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="#7B1FA2"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/tv_list_description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="这是一个静态的List Fragment"
        android:textSize="16sp"
        android:textColor="#424242"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <ListView
        android:id="@+id/lv_items"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@android:color/white"
        android:divider="#E0E0E0"
        android:dividerHeight="1dp"
        android:padding="8dp" />

    <Button
        android:id="@+id/btn_add_item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="添加列表项"
        android:textColor="@android:color/white"
        android:backgroundTint="#7B1FA2"
        android:layout_marginTop="16dp" />

</LinearLayout>
