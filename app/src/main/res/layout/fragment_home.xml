<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:background="#E3F2FD"
    android:padding="16dp">

    <ImageView
        android:id="@+id/iv_home_icon"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:src="@mipmap/ic_launcher"
        android:layout_marginBottom="24dp" />

    <TextView
        android:id="@+id/tv_home_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Home Fragment"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="#1976D2"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/tv_home_description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="这是一个静态的Home Fragment\n用于展示主页内容"
        android:textSize="16sp"
        android:textColor="#424242"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <Button
        android:id="@+id/btn_home_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Home Action"
        android:textColor="@android:color/white"
        android:backgroundTint="#1976D2" />

</LinearLayout>
