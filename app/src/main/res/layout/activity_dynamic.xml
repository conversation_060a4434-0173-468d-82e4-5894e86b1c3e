<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="#FF5722"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="动态Fragment示例"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn_back"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:text="返回"
            android:textSize="12sp"
            android:backgroundTint="@android:color/white"
            android:textColor="#FF5722" />

    </LinearLayout>

    <!-- 控制按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:background="#F5F5F5">

        <Button
            android:id="@+id/btn_home"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_margin="4dp"
            android:text="首页"
            android:textSize="14sp"
            android:backgroundTint="#4CAF50" />

        <Button
            android:id="@+id/btn_list"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_margin="4dp"
            android:text="列表"
            android:textSize="14sp"
            android:backgroundTint="#2196F3" />

        <Button
            android:id="@+id/btn_profile"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_margin="4dp"
            android:text="个人中心"
            android:textSize="14sp"
            android:backgroundTint="#FF9800" />

    </LinearLayout>

    <!-- Fragment容器 -->
    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="#EEEEEE" />

    <!-- 状态显示 -->
    <TextView
        android:id="@+id/tv_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#263238"
        android:padding="12dp"
        android:text="点击上方按钮显示对应Fragment"
        android:textColor="@android:color/white"
        android:textSize="14sp" />

</LinearLayout>
