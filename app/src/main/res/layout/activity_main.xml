<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="#2196F3"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="静态Fragment示例"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- 静态Fragment -->
    <fragment
        android:id="@+id/fragment_home"
        android:name="com.example.myapplication.HomeFragment"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <!-- 跳转到动态Fragment的按钮 -->
    <Button
        android:id="@+id/btn_go_dynamic"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:text="查看动态Fragment示例"
        android:textColor="@android:color/white"
        android:backgroundTint="#FF5722" />

</LinearLayout>
