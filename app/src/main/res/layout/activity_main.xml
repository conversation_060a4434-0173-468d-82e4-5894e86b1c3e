<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="#2196F3"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="静态Fragment示例"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- Fragment容器 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <!-- 静态Fragment - Home -->
        <fragment
            android:id="@+id/fragment_home"
            android:name="com.example.myapplication.HomeFragment"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginEnd="1dp" />

        <!-- 静态Fragment - List -->
        <fragment
            android:id="@+id/fragment_list"
            android:name="com.example.myapplication.ListFragment"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="1dp"
            android:visibility="gone" />

    </FrameLayout>

    <!-- 底部Tab导航 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="#FFFFFF"
        android:elevation="4dp"
        android:orientation="horizontal">

        <RadioGroup
            android:id="@+id/rg_tabs"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/rb_home"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:button="@null"
                android:checked="true"
                android:drawableTop="@drawable/tab_home"
                android:gravity="center"
                android:text="Home"
                android:textColor="#666666"
                android:textSize="12sp" />

            <RadioButton
                android:id="@+id/rb_list"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:button="@null"
                android:drawableTop="@drawable/tab_list"
                android:gravity="center"
                android:text="List"
                android:textColor="#666666"
                android:textSize="12sp" />

        </RadioGroup>

    </LinearLayout>

    <!-- 跳转到动态Fragment的按钮 -->
    <Button
        android:id="@+id/btn_go_dynamic"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:text="查看动态Fragment示例"
        android:textColor="@android:color/white"
        android:backgroundTint="#FF5722" />

</LinearLayout>
